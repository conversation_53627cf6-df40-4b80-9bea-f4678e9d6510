# Seeker Cache Proxy

A high-performance Express.js caching proxy server that forwards requests to the Seeker Social API while providing intelligent Redis-based caching for improved response times.

## Features

- **Proxy Functionality**: Forwards all HTTP methods to `https://dev.seeker.social/api`
- **Redis Caching**: Intelligent caching for GET requests only
- **Configurable Cache Rules**: Path-based caching with customizable TTL
- **Query Parameter Support**: Caches different responses for different query parameter combinations
- **Error Handling**: Graceful handling of Redis and API failures
- **Request Logging**: Comprehensive logging for debugging
- **Health Monitoring**: Built-in health check endpoint

## Prerequisites

- Node.js (v14 or higher)
- Redis server running locally or accessible remotely

## Installation

1. Clone or navigate to the project directory
2. Install dependencies:

   ```bash
   npm install
   ```

3. Configure environment variables by editing `.env`:
   ```env
   PORT=3000
   TARGET_API_BASE_URL=https://dev.seeker.social/api
   REDIS_HOST=localhost
   REDIS_PORT=6379
   REDIS_PASSWORD=
   REDIS_DB=0
   LOG_LEVEL=info
   ```

## Usage

### Start the Server

```bash
npm start
```

The server will start on the configured port (default: 3000) and attempt to connect to Redis.

### Health Check

Check server status and Redis connection:

```bash
curl http://localhost:3000/health
```

### Making Requests

All requests are proxied to the target API. Examples:

```bash
# GET request (will be cached if configured)
curl http://localhost:3000/users

# POST request (not cached)
curl -X POST http://localhost:3000/posts \
  -H "Content-Type: application/json" \
  -d '{"title": "Test Post"}'

# PUT request (not cached)
curl -X PUT http://localhost:3000/users/123 \
  -H "Content-Type: application/json" \
  -d '{"name": "Updated Name"}'
```

## Cache Configuration

Cache rules are defined in `config/cache-config.js`. Each rule specifies:

- `path`: The exact path to cache (without query parameters)
- `ttl`: Time to live in seconds

### Default Cache Rules

```javascript
const cacheConfig = [
  { path: "/users", ttl: 300 }, // 5 minutes
  { path: "/posts", ttl: 600 }, // 10 minutes
  { path: "/profile", ttl: 120 }, // 2 minutes
  { path: "/feed", ttl: 180 }, // 3 minutes
  { path: "/notifications", ttl: 60 }, // 1 minute
  { path: "/settings", ttl: 900 }, // 15 minutes
  { path: "/search", ttl: 240 }, // 4 minutes
];
```

### Adding New Cache Rules

Edit `config/cache-config.js` and add new entries to the `cacheConfig` array:

```javascript
{ path: '/new-endpoint', ttl: 180 } // 3 minutes
```

## Query Parameter Caching

The proxy now supports intelligent caching based on both path and query parameters:

### How It Works

- **Unique Cache Keys**: Each combination of path and query parameters creates a unique cache entry
- **Parameter Normalization**: Query parameters are sorted alphabetically to ensure consistent caching regardless of parameter order
- **URL Encoding**: Special characters in query parameters are properly encoded for cache key generation

### Examples

```bash
# These requests will have different cache entries:
curl "http://localhost:3000/search?lat=48.8698325&lng=2.3428826&radius=100.0&type=bars"
curl "http://localhost:3000/search?lat=40.7128&lng=-74.0060&radius=50.0&type=restaurants"

# These requests will share the same cache entry (parameter order doesn't matter):
curl "http://localhost:3000/search?lat=48.8698325&lng=2.3428826&type=bars&radius=100.0"
curl "http://localhost:3000/search?type=bars&radius=100.0&lat=48.8698325&lng=2.3428826"
```

## Cache Headers

The proxy adds cache-related headers to responses:

- `X-Cache`: `HIT` or `MISS`
- `X-Cache-TTL`: Cache TTL in seconds
- `X-Cache-Key`: The actual cache key used (includes normalized query parameters)
- `X-Proxy`: Always `seeker-cache`
- `X-Target-URL`: The actual URL that was proxied

## Error Handling

The proxy handles various error scenarios:

- **Redis Connection Errors**: Continues without caching
- **API Connection Errors**: Returns 503 Service Unavailable
- **API Timeout Errors**: Returns 504 Gateway Timeout
- **General Errors**: Returns 500 Internal Server Error

## Logging

The server logs:

- All incoming requests with timestamps
- Cache hits and misses
- Redis connection status
- Proxy errors
- Cache operations

## Architecture

```
Client Request → Express Server → Cache Middleware → Proxy → Target API
                      ↓
                 Redis Cache
```

### Components

- **server.js**: Main application entry point
- **middleware/cache.js**: Caching logic for GET requests
- **utils/redis-client.js**: Redis connection and operations
- **config/cache-config.js**: Cache rules configuration

## Development

### Environment Variables

| Variable            | Default                       | Description                  |
| ------------------- | ----------------------------- | ---------------------------- |
| PORT                | 3000                          | Server port                  |
| TARGET_API_BASE_URL | https://dev.seeker.social/api | Target API base URL          |
| REDIS_HOST          | localhost                     | Redis server host            |
| REDIS_PORT          | 6379                          | Redis server port            |
| REDIS_PASSWORD      |                               | Redis password (if required) |
| REDIS_DB            | 0                             | Redis database number        |
| LOG_LEVEL           | info                          | Logging level                |

### Testing

Test the proxy with curl or any HTTP client:

```bash
# Test caching behavior
curl -v http://localhost:3000/users
# First request: X-Cache: MISS
# Second request: X-Cache: HIT
```

## License

ISC
