require("dotenv").config();
const express = require("express");
const cors = require("cors");
const axios = require("axios");
const redisClient = require("./utils/redis-client");
const cacheMiddleware = require("./middleware/cache");

const app = express();
const PORT = process.env.PORT || 3000;
const TARGET_API_BASE_URL =
  process.env.TARGET_API_BASE_URL || "https://dev.seeker.social/api";

// Middleware
app.use(cors());
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "10mb" }));

// Add request logging
app.use((req, _res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Health check endpoint (must be before cache middleware)
app.get("/health", (_req, res) => {
  res.json({
    status: "ok",
    timestamp: new Date().toISOString(),
    redis_connected: redisClient.isConnected,
    target_api: TARGET_API_BASE_URL,
  });
});

// Apply cache middleware
app.use(cacheMiddleware());

// Proxy middleware for all requests (catch-all route)
app.use((req, res) => {
  // Handle proxy logic
  handleProxyRequest(req, res);
});

// Proxy handler function
async function handleProxyRequest(req, res) {
  try {
    const targetUrl = `${TARGET_API_BASE_URL}${req.originalUrl}`;

    console.log(`Proxying ${req.method} request to: ${targetUrl}`);

    // Prepare request configuration
    const config = {
      method: req.method,
      url: targetUrl,
      headers: {
        ...req.headers,
        host: undefined, // Remove host header to avoid conflicts
        "x-forwarded-for": req.ip,
        "x-forwarded-proto": req.protocol,
        "x-forwarded-host": req.get("host"),
      },
      timeout: 30000, // 30 second timeout
      validateStatus: () => true, // Accept all status codes
    };

    // Add request body for methods that support it
    if (["POST", "PUT", "PATCH", "DELETE"].includes(req.method.toUpperCase())) {
      config.data = req.body;
    }

    // Make request to target API
    const response = await axios(config);

    // Forward response headers (excluding some that might cause issues)
    const headersToExclude = [
      "content-encoding",
      "transfer-encoding",
      "connection",
    ];
    Object.keys(response.headers).forEach((header) => {
      if (!headersToExclude.includes(header.toLowerCase())) {
        res.set(header, response.headers[header]);
      }
    });

    // Add proxy headers
    res.set("X-Proxy", "seeker-cache");
    res.set("X-Target-URL", targetUrl);

    // Send response
    res.status(response.status).json(response.data);
  } catch (error) {
    console.error("Proxy error:", error.message);

    if (error.code === "ECONNREFUSED") {
      return res.status(503).json({
        error: "Service Unavailable",
        message: "Unable to connect to target API",
        timestamp: new Date().toISOString(),
      });
    }

    if (error.code === "ETIMEDOUT") {
      return res.status(504).json({
        error: "Gateway Timeout",
        message: "Target API request timed out",
        timestamp: new Date().toISOString(),
      });
    }

    // Generic error response
    res.status(500).json({
      error: "Internal Server Error",
      message: "An error occurred while proxying the request",
      timestamp: new Date().toISOString(),
    });
  }
}

// Error handling middleware
app.use((err, _req, res, _next) => {
  console.error("Unhandled error:", err);
  res.status(500).json({
    error: "Internal Server Error",
    message: "An unexpected error occurred",
    timestamp: new Date().toISOString(),
  });
});

// Initialize Redis connection and start server
async function startServer() {
  try {
    // Connect to Redis
    console.log("Connecting to Redis...");
    await redisClient.connect();

    // Start Express server
    app.listen(PORT, () => {
      console.log(`Seeker Cache Proxy Server running on port ${PORT}`);
      console.log(`Proxying requests to: ${TARGET_API_BASE_URL}`);
      console.log(`Redis connected: ${redisClient.isConnected}`);
    });
  } catch (error) {
    console.error("Failed to start server:", error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on("SIGINT", async () => {
  console.log("\nShutting down gracefully...");
  await redisClient.disconnect();
  process.exit(0);
});

process.on("SIGTERM", async () => {
  console.log("\nShutting down gracefully...");
  await redisClient.disconnect();
  process.exit(0);
});

// Start the server
startServer();
