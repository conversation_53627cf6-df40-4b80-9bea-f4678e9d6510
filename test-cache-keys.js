#!/usr/bin/env node

/**
 * Test script to verify cache key generation with query parameters
 * Run with: node test-cache-keys.js
 */

const redisClient = require('./utils/redis-client');

function testCacheKeyGeneration() {
  console.log('🔑 Testing Cache Key Generation...\n');

  // Test 1: Path only
  console.log('1. Testing path-only cache key generation:');
  const pathOnlyKey = redisClient.generateCacheKey('/search');
  console.log(`   Path: /search`);
  console.log(`   Cache Key: ${pathOnlyKey}\n`);

  // Test 2: Path with query parameters
  console.log('2. Testing path with query parameters:');
  const queryParams1 = { lat: '48.8698325', lng: '2.3428826', radius: '100.0', type: 'bars' };
  const queryKey1 = redisClient.generateCacheKey('/search', queryParams1);
  console.log(`   Path: /search`);
  console.log(`   Query Params: ${JSON.stringify(queryParams1)}`);
  console.log(`   Cache Key: ${queryKey1}\n`);

  // Test 3: Same parameters in different order (should produce same key)
  console.log('3. Testing parameter order normalization:');
  const queryParams2 = { type: 'bars', radius: '100.0', lng: '2.3428826', lat: '48.8698325' };
  const queryKey2 = redisClient.generateCacheKey('/search', queryParams2);
  console.log(`   Path: /search`);
  console.log(`   Query Params: ${JSON.stringify(queryParams2)}`);
  console.log(`   Cache Key: ${queryKey2}`);
  console.log(`   Keys Match: ${queryKey1 === queryKey2 ? '✅ YES' : '❌ NO'}\n`);

  // Test 4: Different parameters (should produce different key)
  console.log('4. Testing different parameters:');
  const queryParams3 = { lat: '40.7128', lng: '-74.0060', radius: '50.0', type: 'restaurants' };
  const queryKey3 = redisClient.generateCacheKey('/search', queryParams3);
  console.log(`   Path: /search`);
  console.log(`   Query Params: ${JSON.stringify(queryParams3)}`);
  console.log(`   Cache Key: ${queryKey3}`);
  console.log(`   Different from first: ${queryKey1 !== queryKey3 ? '✅ YES' : '❌ NO'}\n`);

  // Test 5: Special characters and encoding
  console.log('5. Testing special characters and encoding:');
  const queryParams4 = { q: 'café & restaurant', location: 'New York, NY' };
  const queryKey4 = redisClient.generateCacheKey('/search', queryParams4);
  console.log(`   Path: /search`);
  console.log(`   Query Params: ${JSON.stringify(queryParams4)}`);
  console.log(`   Cache Key: ${queryKey4}\n`);

  // Test 6: Array values
  console.log('6. Testing array values:');
  const queryParams5 = { tags: ['bar', 'restaurant'], category: 'food' };
  const queryKey5 = redisClient.generateCacheKey('/search', queryParams5);
  console.log(`   Path: /search`);
  console.log(`   Query Params: ${JSON.stringify(queryParams5)}`);
  console.log(`   Cache Key: ${queryKey5}\n`);

  // Test 7: Empty and null values
  console.log('7. Testing empty and null values:');
  const queryParams6 = { lat: '48.8698325', lng: '', radius: null, type: undefined };
  const queryKey6 = redisClient.generateCacheKey('/search', queryParams6);
  console.log(`   Path: /search`);
  console.log(`   Query Params: ${JSON.stringify(queryParams6)}`);
  console.log(`   Cache Key: ${queryKey6}\n`);

  console.log('✅ Cache key generation tests completed!');
}

// Run tests
testCacheKeyGeneration();
