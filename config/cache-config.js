/**
 * Cache configuration for different API paths
 * Each entry defines a path pattern and its cache TTL in seconds
 */

const cacheConfig = [
  { path: "/clubdetail", ttl: 900 },
  { path: "/clubs_by_tags_view", ttl: 900 },
  { path: "/nearToYou", ttl: 900 },
  { path: "/terms-and-conditions", ttl: 900 },
  { path: "/search", ttl: 900 },
  { path: "/categories", ttl: 900 },
  { path: "/mapsdata", ttl: 900 },
];

/**
 * Get cache configuration for a specific path
 * @param {string} path - The request path (without query parameters)
 * @returns {object|null} - Cache config object or null if not cacheable
 */
function getCacheConfig(path) {
  return cacheConfig.find((config) => config.path === path) || null;
}

/**
 * Check if a path should be cached
 * @param {string} path - The request path (without query parameters)
 * @returns {boolean} - True if path should be cached
 */
function isCacheable(path) {
  return cacheConfig.some((config) => config.path === path);
}

module.exports = {
  cacheConfig,
  getCacheConfig,
  isCacheable,
};
