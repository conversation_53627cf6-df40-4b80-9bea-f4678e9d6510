#!/usr/bin/env node

/**
 * Simple test script to verify proxy functionality
 * Run with: node test-proxy.js
 */

const axios = require("axios");

const PROXY_URL = "http://localhost:3000";

async function testProxy() {
  console.log("🚀 Testing Seeker Cache Proxy...\n");

  try {
    // Test 1: Health check
    console.log("1. Testing health check...");
    const healthResponse = await axios.get(`${PROXY_URL}/health`);
    console.log("✅ Health check passed");
    console.log(`   Status: ${healthResponse.data.status}`);
    console.log(`   Redis connected: ${healthResponse.data.redis_connected}`);
    console.log(`   Target API: ${healthResponse.data.target_api}\n`);

    // Test 2: Test caching with a GET request (no query params)
    console.log("2. Testing GET request caching (no query params)...");

    // First request (should be MISS)
    console.log("   Making first request (expecting cache MISS)...");
    const firstResponse = await axios.get(`${PROXY_URL}/search`, {
      validateStatus: () => true, // Accept all status codes
    });
    console.log(`   Status: ${firstResponse.status}`);
    console.log(`   X-Cache: ${firstResponse.headers["x-cache"] || "Not set"}`);
    console.log(
      `   X-Cache-Key: ${firstResponse.headers["x-cache-key"] || "Not set"}`
    );

    // Wait a moment
    await new Promise((resolve) => setTimeout(resolve, 100));

    // Second request (should be HIT if caching is working)
    console.log("   Making second request (expecting cache HIT)...");
    const secondResponse = await axios.get(`${PROXY_URL}/search`, {
      validateStatus: () => true,
    });
    console.log(`   Status: ${secondResponse.status}`);
    console.log(
      `   X-Cache: ${secondResponse.headers["x-cache"] || "Not set"}`
    );
    console.log(
      `   X-Cache-Key: ${secondResponse.headers["x-cache-key"] || "Not set"}\n`
    );

    // Test 3: Test caching with query parameters
    console.log("3. Testing GET request caching with query parameters...");

    // First request with specific query params (should be MISS)
    console.log(
      "   Making first request with query params (expecting cache MISS)..."
    );
    const queryParams1 = {
      lat: "48.8698325",
      lng: "2.3428826",
      radius: "100.0",
      type: "bars",
    };
    const firstQueryResponse = await axios.get(`${PROXY_URL}/search`, {
      params: queryParams1,
      validateStatus: () => true,
    });
    console.log(`   Status: ${firstQueryResponse.status}`);
    console.log(
      `   X-Cache: ${firstQueryResponse.headers["x-cache"] || "Not set"}`
    );
    console.log(
      `   X-Cache-Key: ${
        firstQueryResponse.headers["x-cache-key"] || "Not set"
      }`
    );

    // Second request with same query params (should be HIT)
    console.log(
      "   Making second request with same query params (expecting cache HIT)..."
    );
    const secondQueryResponse = await axios.get(`${PROXY_URL}/search`, {
      params: queryParams1,
      validateStatus: () => true,
    });
    console.log(`   Status: ${secondQueryResponse.status}`);
    console.log(
      `   X-Cache: ${secondQueryResponse.headers["x-cache"] || "Not set"}`
    );
    console.log(
      `   X-Cache-Key: ${
        secondQueryResponse.headers["x-cache-key"] || "Not set"
      }`
    );

    // Third request with different query params (should be MISS)
    console.log(
      "   Making third request with different query params (expecting cache MISS)..."
    );
    const queryParams2 = {
      lat: "40.7128",
      lng: "-74.0060",
      radius: "50.0",
      type: "restaurants",
    };
    const thirdQueryResponse = await axios.get(`${PROXY_URL}/search`, {
      params: queryParams2,
      validateStatus: () => true,
    });
    console.log(`   Status: ${thirdQueryResponse.status}`);
    console.log(
      `   X-Cache: ${thirdQueryResponse.headers["x-cache"] || "Not set"}`
    );
    console.log(
      `   X-Cache-Key: ${
        thirdQueryResponse.headers["x-cache-key"] || "Not set"
      }\n`
    );

    // Test 4: Test query parameter ordering consistency
    console.log("4. Testing query parameter ordering consistency...");

    // Request with params in one order
    const paramsOrder1 = {
      type: "bars",
      lat: "48.8698325",
      radius: "100.0",
      lng: "2.3428826",
    };
    const orderTest1 = await axios.get(`${PROXY_URL}/search`, {
      params: paramsOrder1,
      validateStatus: () => true,
    });
    console.log(
      `   First order - X-Cache: ${orderTest1.headers["x-cache"] || "Not set"}`
    );
    console.log(
      `   First order - X-Cache-Key: ${
        orderTest1.headers["x-cache-key"] || "Not set"
      }`
    );

    // Request with same params in different order (should be HIT due to normalization)
    const paramsOrder2 = {
      lng: "2.3428826",
      radius: "100.0",
      type: "bars",
      lat: "48.8698325",
    };
    const orderTest2 = await axios.get(`${PROXY_URL}/search`, {
      params: paramsOrder2,
      validateStatus: () => true,
    });
    console.log(
      `   Different order - X-Cache: ${
        orderTest2.headers["x-cache"] || "Not set"
      }`
    );
    console.log(
      `   Different order - X-Cache-Key: ${
        orderTest2.headers["x-cache-key"] || "Not set"
      }\n`
    );

    // Test 5: Test non-cacheable request
    console.log("5. Testing POST request (should not be cached)...");
    const postResponse = await axios.post(
      `${PROXY_URL}/posts`,
      {
        title: "Test Post",
        content: "This is a test post",
      },
      {
        validateStatus: () => true,
      }
    );
    console.log(`   Status: ${postResponse.status}`);
    console.log(
      `   X-Cache: ${
        postResponse.headers["x-cache"] || "Not set (expected for POST)"
      }`
    );
    console.log(
      `   X-Proxy: ${postResponse.headers["x-proxy"] || "Not set"}\n`
    );

    // Test 6: Test non-configured path
    console.log("6. Testing non-configured path (should not be cached)...");
    const nonCachedResponse = await axios.get(
      `${PROXY_URL}/non-cached-endpoint`,
      {
        validateStatus: () => true,
      }
    );
    console.log(`   Status: ${nonCachedResponse.status}`);
    console.log(
      `   X-Cache: ${
        nonCachedResponse.headers["x-cache"] ||
        "Not set (expected for non-configured path)"
      }`
    );
    console.log(
      `   X-Proxy: ${nonCachedResponse.headers["x-proxy"] || "Not set"}\n`
    );

    console.log("✅ All tests completed successfully!");
  } catch (error) {
    console.error("❌ Test failed:", error.message);

    if (error.code === "ECONNREFUSED") {
      console.error("   Make sure the proxy server is running on port 3000");
      console.error("   Run: npm start");
    }

    process.exit(1);
  }
}

// Run tests
testProxy();
