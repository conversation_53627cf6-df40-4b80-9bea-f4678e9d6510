#!/usr/bin/env node

/**
 * Simple test script to verify proxy functionality
 * Run with: node test-proxy.js
 */

const axios = require('axios');

const PROXY_URL = 'http://localhost:3000';

async function testProxy() {
  console.log('🚀 Testing Seeker Cache Proxy...\n');

  try {
    // Test 1: Health check
    console.log('1. Testing health check...');
    const healthResponse = await axios.get(`${PROXY_URL}/health`);
    console.log('✅ Health check passed');
    console.log(`   Status: ${healthResponse.data.status}`);
    console.log(`   Redis connected: ${healthResponse.data.redis_connected}`);
    console.log(`   Target API: ${healthResponse.data.target_api}\n`);

    // Test 2: Test caching with a GET request
    console.log('2. Testing GET request caching...');
    
    // First request (should be MISS)
    console.log('   Making first request (expecting cache MISS)...');
    const firstResponse = await axios.get(`${PROXY_URL}/users`, {
      validateStatus: () => true // Accept all status codes
    });
    console.log(`   Status: ${firstResponse.status}`);
    console.log(`   X-Cache: ${firstResponse.headers['x-cache'] || 'Not set'}`);
    console.log(`   X-Cache-TTL: ${firstResponse.headers['x-cache-ttl'] || 'Not set'}`);

    // Wait a moment
    await new Promise(resolve => setTimeout(resolve, 100));

    // Second request (should be HIT if caching is working)
    console.log('   Making second request (expecting cache HIT)...');
    const secondResponse = await axios.get(`${PROXY_URL}/users`, {
      validateStatus: () => true
    });
    console.log(`   Status: ${secondResponse.status}`);
    console.log(`   X-Cache: ${secondResponse.headers['x-cache'] || 'Not set'}`);
    console.log(`   X-Cache-TTL: ${secondResponse.headers['x-cache-ttl'] || 'Not set'}\n`);

    // Test 3: Test non-cacheable request
    console.log('3. Testing POST request (should not be cached)...');
    const postResponse = await axios.post(`${PROXY_URL}/posts`, {
      title: 'Test Post',
      content: 'This is a test post'
    }, {
      validateStatus: () => true
    });
    console.log(`   Status: ${postResponse.status}`);
    console.log(`   X-Cache: ${postResponse.headers['x-cache'] || 'Not set (expected for POST)'}`);
    console.log(`   X-Proxy: ${postResponse.headers['x-proxy'] || 'Not set'}\n`);

    // Test 4: Test non-configured path
    console.log('4. Testing non-configured path (should not be cached)...');
    const nonCachedResponse = await axios.get(`${PROXY_URL}/non-cached-endpoint`, {
      validateStatus: () => true
    });
    console.log(`   Status: ${nonCachedResponse.status}`);
    console.log(`   X-Cache: ${nonCachedResponse.headers['x-cache'] || 'Not set (expected for non-configured path)'}`);
    console.log(`   X-Proxy: ${nonCachedResponse.headers['x-proxy'] || 'Not set'}\n`);

    console.log('✅ All tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('   Make sure the proxy server is running on port 3000');
      console.error('   Run: npm start');
    }
    
    process.exit(1);
  }
}

// Run tests
testProxy();
