/**
 * Returns the reverse rank of one or more values in a t-digest sketch (number of values that are higher than each value)
 * @param parser - The command parser
 * @param key - The name of the t-digest sketch
 * @param values - Array of values to get reverse ranks for
 */
declare const _default: {
    readonly IS_READ_ONLY: true;
    readonly parseCommand: (this: void, parser: import("@redis/client").CommandParser, key: import("@redis/client/dist/lib/RESP/types").RedisArgument, values: number[]) => void;
    readonly transformReply: () => import("@redis/client/dist/lib/RESP/types").ArrayReply<import("@redis/client/dist/lib/RESP/types").NumberReply<number>>;
};
export default _default;
//# sourceMappingURL=REVRANK.d.ts.map