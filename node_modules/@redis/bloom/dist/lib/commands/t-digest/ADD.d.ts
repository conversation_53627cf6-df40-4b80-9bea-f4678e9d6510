import { CommandParser } from '@redis/client/dist/lib/client/parser';
import { SimpleStringReply, RedisArgument } from '@redis/client/dist/lib/RESP/types';
declare const _default: {
    readonly IS_READ_ONLY: false;
    /**
     * Adds one or more observations to a t-digest sketch
     * @param parser - The command parser
     * @param key - The name of the t-digest sketch
     * @param values - Array of numeric values to add to the sketch
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, values: Array<number>) => void;
    readonly transformReply: () => SimpleStringReply<'OK'>;
};
export default _default;
//# sourceMappingURL=ADD.d.ts.map