import { CommandParser } from '@redis/client/dist/lib/client/parser';
import { RedisArgument } from '@redis/client/dist/lib/RESP/types';
declare const _default: {
    readonly IS_READ_ONLY: false;
    /**
     * Adds an item to a Bloom Filter
     * @param parser - The command parser
     * @param key - The name of the Bloom filter
     * @param item - The item to add to the filter
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, item: RedisArgument) => void;
    readonly transformReply: {
        2: (reply: import("@redis/client/dist/lib/RESP/types").NumberReply<0 | 1>) => boolean;
        3: () => import("@redis/client/dist/lib/RESP/types").BooleanReply<boolean>;
    };
};
export default _default;
//# sourceMappingURL=ADD.d.ts.map